import Vue from "vue";
import VueRouter from "vue-router";
import settings from "@/settings";
import routerPermission from "../../src/permission";
/* Layout */
import Layout from "@/layout";
import SubMenuContainer from "@/layout/components/SubMenuContainer.vue";

/* Router Modules */
import request from "@/utils/request";

/**routers */
// import allrouter from "./allRouter";
// import netrouter from "./netRouter";
/**
 * qiankunjs--基座--注册信息引入---开始
 */
//引入qiankunjs的配置文件
// import { registerMicroAppsData } from "@/MicroConfig";
/**
 * qiankunjs--基座--注册信息引入---结束
 */
// import userRouter from "@/views/sys/sysuser/user/router";
// import sysCompanyRouter from "@/views/sys/syscompany/router";
// import robotRouter from "@/views/robotDemo/robots/robot/router";
// import sysBaseSddRouter from "@/views/sys/base/sdd/router";
// import sysBaseQuartzRouter from "@/views/sys/base/quartz/router";
// import dashboardRouter from "@/views/dashboard/router";

Vue.use(VueRouter);

const originalPush = VueRouter.prototype.push;
VueRouter.prototype.push = function push(location, onResolve, onReject) {
    if (onResolve || onReject)
        return originalPush.call(this, location, onResolve, onReject);
    return originalPush.call(this, location).catch((err) => err);
};

/**
 * Note: sub-menu only appear when route children.length >= 1
 * Detail see: https://panjiachen.github.io/vue-element-admin-site/guide/essentials/router-and-nav.html
 *
 * hidden: true                   if set true, item will not show in the sidebar(default is false)
 * alwaysShow: true               if set true, will always show the root menu
 *                                if not set alwaysShow, when item has more than one children route,
 *                                it will becomes nested mode, otherwise not show the root menu
 * redirect: noRedirect           if set noRedirect will no redirect in the breadcrumb
 * name:'router-name'             the name is used by <keep-alive> (must set!!!)
 * meta : {
    roles: ['admin','editor']    control the page roles (you can set multiple roles)
    title: 'title'               the name show in sidebar and breadcrumb (recommend set)
    icon: 'svg-name'             the icon show in the sidebar
    noCache: true                if set true, the page will no be cached(default is false)
    affix: true                  if set true, the tag will affix in the tags-view
    breadcrumb: false            if set false, the item will hidden in breadcrumb(default is true)
    activeMenu: '/example/list'  if set path, the sidebar will highlight the path you set
  }
 */

/**
 * curRouter
 * 根据部署的网络环境不通引入不通的路由数据
 */
const curRouter = process.env.VUE_APP_ENV === 'internet' ? require("./internetRouter") : require("./intranetRouter");

/**
 * constantRoutes
 * a base page that does not have permission requirements
 * all roles can be accessed
 */
export const constantRoutes = [
    // 内嵌不显示的页面面路由存放位置，需要新开标签页时在这里添加
    // userRouter,
    // sysCompanyRouter,
    // robotRouter,
    // sysBaseSddRouter,
    // sysBaseQuartzRouter,
    // dashboardRouter,
    {
        path: "/redirect",
        component: Layout,
        hidden: true,
        children: [
            {
                path: "/redirect/:path*",
                component: () => import("@/views/redirect/index")
            }
        ]
    },
    {
        path: "/login",
        name: "login",
        component: () => import("@/views/login/index"),
        hidden: true
    },
    {
        path: "/auth-redirect",
        component: () => import("@/views/login/auth-redirect"),
        hidden: true
    },
    // {
    //     path: "/404",
    //     component: () => import("@/views/error-page/404"),
    //     hidden: true
    // },
    {
        path: "/401",
        component: () => import("@/views/error-page/401"),
        hidden: true
    },
    {
        path: "/",
        component: Layout,
        redirect: "dashboard",
        hidden: true,
        children: [
            {
                path: "dashboard",
                component: () => import("@/views/dashboard/index"),
                name: "dashboard",
                title: "Dashboard",
                meta: { title: "dashboard", icon: "dashboard", affix: true }
            },
            {
                path: "404",
                component: () => import("@/views/error-page/404"),
                name: "400",
                title: "页面不存在",
                meta: { title: "页面不存在" }
            },
            {
                path: "500",
                component: () => import("@/views/error-page/500"),
                name: "500",
                title: "服务器错误",
                meta: { title: "服务器错误" }
            }
        ]
    }, {
        path: "/paasPortal/",
        component: () => import("@/layout"),
        hidden: true,
        // redirect:"portal",
        children: [
            {
                path: "*",
                component: () => import("@/MicroContainer.vue"),
                name: "portal",
                title: "portal",
                meta: { title: "portal", icon: "", affix: false }
            }
        ]
    },
    // {
    //     path: "/paasPortal/",
    //     component: () => import("@/layout"),
    //     hidden: true,
    //     // redirect:"portal",
    //     children: [
    //         {
    //             path: "*",
    //             component: () => import("@/MicroContainer.vue"),
    //             name: "portal",
    //             title: "portal",
    //             meta: { title: "portal", icon: "", affix: false }
    //         }
    //     ]
    // }
    // ...curRouter
];
// .concat(curRouter.default);

/**
 * asyncRoutes
 * the routes that need to be dynamically loaded based on user roles
 */
export const asyncRoutes = [
    ...curRouter.default
];
const createRouter = () => new VueRouter({
    // eslint-disable-line no-undef
    // base: "test",
    /**
         * qiankunjs--子应用--路由base属性---开始
         */

    base: process.env.VUE_APP_USE_TYPE === "MicroApp" && window.__POWERED_BY_QIANKUN__ ? "/paasPortal/test" : "",
    /**
      * qiankunjs--子应用--路由base属性---结束
      */
    mode: "hash", // 使用hash模式避免刷新404问题
    scrollBehavior: () => ({ y: 0 }),
    routes: constantRoutes.filter((item) => {
        if (settings.loginActive) {
            return item;
        } else {
            return item.path.indexOf("login") < 0;
        }
    })
});
// const router = createRouter();

// Detail see: https://github.com/vuejs/vue-router/issues/1234#issuecomment-357941465
export function resetRouter() {
    const newRouter = createRouter();
    router.matcher = newRouter.matcher; // reset router
}
// 获取后台的动态路由
export const getRoutersFromService = () => {
    return request({
        url: "/api/misc/sys/base/menu/systemMenu/platform",
        method: "get"
    });
};
// export default router;
//导出router，便于乾坤清除
let router = null;
export function routerFun() {
    if (router === null) {
        router = createRouter();
        routerPermission(router);
    }
    return router;
}
export function destoryRouterFun() {
    router = null;
}
