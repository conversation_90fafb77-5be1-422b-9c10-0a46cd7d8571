user nginx;
worker_processes  4;

error_log /data/log/nginx/error.log  error;


events {
    worker_connections  65535;
}


http {
    include       mime.types;
    default_type  application/octet-stream;
    client_max_body_size 200m;
    server_tokens  off; # 隐藏Nginx的版本信息
    #log_format  main  '$remote_addr - $remote_user [$time_local] "$request" '
    #                  '$status $body_bytes_sent "$http_referer" '
    #                  '"$http_user_agent" "$http_x_forwarded_for"';
    log_format main 'server="$server_name" host="$host” dest_port="$server_port"'
                    #'src="$remote_addr" ip="$realip_remote_addr" user="$remote_user" '
                    'time_local="$time_local" http_status="$status" '
                    'http_referer="$http_referer" http_user_agent="$http_user_agent" '
                    'http_x_forwarded_for="$http_x_forwarded_for" '
                    'http_x_header="$http_x_header" uri_query="$query_string" uri_path="$uri" '
                    'request=$request http_method="$request_method" ';
    access_log /data/log/nginx/access.log main;

    sendfile        on;
    #tcp_nopush     on;

    #keepalive_timeout  0;
    keepalive_timeout  65;

    # 打开gzip
    gzip  on;
    gzip_min_length 1k;
    gzip_buffers    32 4k;
    gzip_http_version 1.0;
    gzip_comp_level 5;
    gzip_types text/plain text/css application/json application/javascript application/xml;
    gzip_vary on;
    gzip_proxied off;

    #gzip  on;

	server {
        listen  9006;
        server_name  localhost;
        charset utf-8;
        # 禁止访问隐藏文件
        location ~ /\. {deny all; return 404; }

        location /status {
            stub_status on;
        }
        root  /demo/main;
        try_files  $uri $uri/ /index.html;
        location ~* .*\.(js|css)$ {
            add_header Cache-Control max-age=3600;
            expires 1h;

            etag off; # 集群部署状态下建议关闭，单机部署建议开启
            #编码格式
            charset utf-8;
            set $args "$args&ip=$remote_addr";  #将remote_addr记录到请求串ip参数中

            #add_header Content-Security-Policy upgrade-insecure-requests;

        }
        location ~* .*\.(ttf|woff)$ {
            add_header Cache-Control max-age=2592000;
            expires 30d;

            etag off; # 集群部署状态下建议关闭，单机部署建议开启
            #编码格式
            charset utf-8;
            set $args "$args&ip=$remote_addr";  #将remote_addr记录到请求串ip参数中

            #add_header Content-Security-Policy upgrade-insecure-requests;

        }
        location ~* .*\.(jpg|jpeg|gif|png|ico|bmp|swf|svg)$ {
            add_header Cache-Control max-age=86400;
            expires 1d;

            etag off; # 集群部署状态下建议关闭，单机部署建议开启
            #编码格式
            charset utf-8;
            set $args "$args&ip=$remote_addr";  #将remote_addr记录到请求串ip参数中

            #add_header Content-Security-Policy upgrade-insecure-requests;

        }
        location ~* .*\.(htm|html)$ {
            expires 0;
            add_header Cache-Control no-store;
            add_header Pragma  no-store;

            etag off; # 集群部署状态下建议关闭，单机部署建议开启
            #编码格式
            charset utf-8;
            set $args "$args&ip=$remote_addr";  #将remote_addr记录到请求串ip参数中

            #add_header Content-Security-Policy upgrade-insecure-requests;

        }
        error_page   500 502 503 504  /50x.html;
        location = /50x.html {
            root   html;
        }
    }



	server {
        listen  9008;
        server_name  localhost;
        charset utf-8;

        # 禁止访问隐藏文件
        location ~ /\. {deny all; return 404; }

        location /status {
            stub_status on;
        }
        root  /demo/sub9008;
        try_files  $uri $uri/ /index.html;
        location ~* .*\.(js|css)$ {
            add_header Access-Control-Allow-Origin *;
			add_header Access-Control-Allow-Methods 'GET, POST, OPTIONS';
			add_header Access-Control-Allow-Headers 'DNT,X-Mx-ReqToken,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Authorization';
            add_header Cache-Control max-age=3600;
            expires 1h;

            etag off; # 集群部署状态下建议关闭，单机部署建议开启
            #编码格式
            charset utf-8;
            set $args "$args&ip=$remote_addr";  #将remote_addr记录到请求串ip参数中

            #add_header Content-Security-Policy upgrade-insecure-requests;

        }
        location ~* .*\.(ttf|woff)$ {
            add_header Access-Control-Allow-Origin *;
			add_header Access-Control-Allow-Methods 'GET, POST, OPTIONS';
			add_header Access-Control-Allow-Headers 'DNT,X-Mx-ReqToken,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Authorization';
            add_header Cache-Control max-age=2592000;
            expires 30d;

            etag off; # 集群部署状态下建议关闭，单机部署建议开启
            #编码格式
            charset utf-8;
            set $args "$args&ip=$remote_addr";  #将remote_addr记录到请求串ip参数中

            #add_header Content-Security-Policy upgrade-insecure-requests;

        }
        location ~* .*\.(jpg|jpeg|gif|png|ico|bmp|swf|svg)$ {
            add_header Access-Control-Allow-Origin *;
			add_header Access-Control-Allow-Methods 'GET, POST, OPTIONS';
			add_header Access-Control-Allow-Headers 'DNT,X-Mx-ReqToken,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Authorization';
            add_header Cache-Control max-age=86400;
            expires 1d;

            etag off; # 集群部署状态下建议关闭，单机部署建议开启
            #编码格式
            charset utf-8;
            set $args "$args&ip=$remote_addr";  #将remote_addr记录到请求串ip参数中

            #add_header Content-Security-Policy upgrade-insecure-requests;

        }
        location ~* .*\.(htm|html)$ {
            add_header Access-Control-Allow-Origin *;
			add_header Access-Control-Allow-Methods 'GET, POST, OPTIONS';
			add_header Access-Control-Allow-Headers 'DNT,X-Mx-ReqToken,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Authorization';
            expires 0;
            add_header Cache-Control no-store;
            add_header Pragma  no-store;

            etag off; # 集群部署状态下建议关闭，单机部署建议开启
            #编码格式
            charset utf-8;
            set $args "$args&ip=$remote_addr";  #将remote_addr记录到请求串ip参数中

            #add_header Content-Security-Policy upgrade-insecure-requests;

        }
        error_page   500 502 503 504  /50x.html;
        location = /50x.html {
            root   html;
        }
    }


    server {
        listen  8080;
        server_name  localhost;
        charset utf-8;

        # 禁止访问隐藏文件
        location ~ /\. {deny all; return 404; }

        location /status {
            stub_status on;
        }
        root  /home/<USER>/tzlib/renbao/pdfc-web-*******/dist;
        try_files  $uri $uri/ /index.html;

        location ~* .*\.(js|css)$ {
            add_header Cache-Control max-age=3600;
            expires 1h;
            etag off;
            charset utf-8;
        }
        location ~* .*\.(ttf|woff)$ {
            add_header Cache-Control max-age=2592000;
            expires 30d;
            etag off;
            charset utf-8;
        }
        location ~* .*\.(jpg|jpeg|gif|png|ico|bmp|swf|svg)$ {
            add_header Cache-Control max-age=86400;
            expires 1d;
            etag off;
            charset utf-8;
        }
        location ~* .*\.(htm|html)$ {
            expires 0;
            add_header Cache-Control no-store;
            add_header Pragma  no-store;
            etag off;
            charset utf-8;
        }
        error_page   500 502 503 504  /50x.html;
        location = /50x.html {
            root   html;
        }
    }

    server {
        listen  9009;
        server_name  localhost;
        charset utf-8;

        # 禁止访问隐藏文件
        location ~ /\. {deny all; return 404; }

        location /status {
            stub_status on;
        }
        root  /demo/sub9009;
        try_files  $uri $uri/ /index.html;
        location ~* .*\.(js|css)$ {
            add_header Access-Control-Allow-Origin *;
			add_header Access-Control-Allow-Methods 'GET, POST, OPTIONS';
			add_header Access-Control-Allow-Headers 'DNT,X-Mx-ReqToken,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Authorization';
            add_header Cache-Control max-age=3600;
            expires 1h;

            etag off; # 集群部署状态下建议关闭，单机部署建议开启
            #编码格式
            charset utf-8;
            set $args "$args&ip=$remote_addr";  #将remote_addr记录到请求串ip参数中

            #add_header Content-Security-Policy upgrade-insecure-requests;

        }
        location ~* .*\.(ttf|woff)$ {
            add_header Access-Control-Allow-Origin *;
			add_header Access-Control-Allow-Methods 'GET, POST, OPTIONS';
			add_header Access-Control-Allow-Headers 'DNT,X-Mx-ReqToken,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Authorization';
            add_header Cache-Control max-age=2592000;
            expires 30d;

            etag off; # 集群部署状态下建议关闭，单机部署建议开启
            #编码格式
            charset utf-8;
            set $args "$args&ip=$remote_addr";  #将remote_addr记录到请求串ip参数中

            #add_header Content-Security-Policy upgrade-insecure-requests;

        }
        location ~* .*\.(jpg|jpeg|gif|png|ico|bmp|swf|svg)$ {
            add_header Access-Control-Allow-Origin *;
			add_header Access-Control-Allow-Methods 'GET, POST, OPTIONS';
			add_header Access-Control-Allow-Headers 'DNT,X-Mx-ReqToken,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Authorization';
            add_header Cache-Control max-age=86400;
            expires 1d;

            etag off; # 集群部署状态下建议关闭，单机部署建议开启
            #编码格式
            charset utf-8;
            set $args "$args&ip=$remote_addr";  #将remote_addr记录到请求串ip参数中

            #add_header Content-Security-Policy upgrade-insecure-requests;

        }
        location ~* .*\.(htm|html)$ {
            add_header Access-Control-Allow-Origin *;
			add_header Access-Control-Allow-Methods 'GET, POST, OPTIONS';
			add_header Access-Control-Allow-Headers 'DNT,X-Mx-ReqToken,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Authorization';
            expires 0;
            add_header Cache-Control no-store;
            add_header Pragma  no-store;

            etag off; # 集群部署状态下建议关闭，单机部署建议开启
            #编码格式
            charset utf-8;
            set $args "$args&ip=$remote_addr";  #将remote_addr记录到请求串ip参数中

            #add_header Content-Security-Policy upgrade-insecure-requests;

        }
        error_page   500 502 503 504  /50x.html;
        location = /50x.html {
            root   html;
        }
    }




}
